{"info": {"_postman_id": "member-diagnoses-medications-collection", "name": "HMBL Core - Member Diagnoses, Medications & WHO ICD API", "description": "Complete Postman collection for Member Diagnoses and Medications controllers with WHO ICD API integration. Includes endpoints for searching ICD-10 and ICD-11 classifications, retrieving detailed disease information, and creating diagnoses with standardized medical codes.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "memberID", "value": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, {"key": "diagnosisID", "value": "123e4567-e89b-12d3-a456-426614174001", "type": "string"}, {"key": "medicationID", "value": "123e4567-e89b-12d3-a456-426614174002", "type": "string"}, {"key": "authToken", "value": "your-jwt-token-here", "type": "string"}, {"key": "whoEntityId", "value": "142601234", "type": "string"}], "item": [{"name": "WHO ICD API", "description": "WHO International Classification of Diseases API endpoints for searching diagnoses and disease classifications", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/whoicd/health", "host": ["{{baseUrl}}"], "path": ["whoicd", "health"]}, "description": "Check the health status of the WHO ICD API integration"}}, {"name": "Search Diagnoses (General)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"diabetes mellitus\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}, "description": "Search for diagnoses across the default ICD release (ICD-11 2024-01)"}}, {"name": "Search ICD-10 Diagnoses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"hypertension\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd10", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd10"]}, "description": "Search specifically within ICD-10 classification (release 2019-04)"}}, {"name": "Search ICD-11 Diagnoses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"covid-19\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd11", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd11"]}, "description": "Search specifically within ICD-11 classification (release 2024-01)"}}, {"name": "Combined Search (ICD-10 & ICD-11)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"pneumonia\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}, "description": "Search across both ICD-10 and ICD-11 classifications simultaneously"}}, {"name": "Get Entity Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/entity/{{whoEntityId}}", "host": ["{{baseUrl}}"], "path": ["whoicd", "entity", "{{whoEntityId}}"]}, "description": "Retrieve detailed information about a specific ICD entity"}}, {"name": "Test Authentication Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/token/test", "host": ["{{baseUrl}}"], "path": ["whoicd", "token", "test"]}, "description": "Test the WHO ICD API authentication (for debugging purposes)"}}]}, {"name": "WHO ICD Examples", "description": "Common medical condition search examples using WHO ICD API", "item": [{"name": "Search - Heart Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"heart failure\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}}}, {"name": "Search - Respiratory Conditions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"asthma\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}}}, {"name": "Search - Mental Health", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"depression\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/icd11", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "icd11"]}}}, {"name": "Search - Infectious Diseases", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"influenza\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}}}, {"name": "Search - Cancer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"breast cancer\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search", "host": ["{{baseUrl}}"], "path": ["whoicd", "search"]}}}]}, {"name": "Member Diagnoses", "item": [{"name": "Create Diagnosis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP remains elevated despite medication adjustment. Patient reports compliance with current regimen.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create a new diagnosis for a member. Use WHO ICD API to search for appropriate ICD codes first."}}, {"name": "Create Diagnosis (WHO ICD Example)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"5A11\",\n  \"description\": \"Type 2 diabetes mellitus\",\n  \"clinicalNote\": \"Diagnosed based on elevated HbA1c (8.2%) and fasting glucose levels. Patient has family history of diabetes.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-15T00:00:00Z\",\n  \"source\": \"WHO ICD search\",\n  \"confirmedBy\": \"<PERSON><PERSON>, MD\",\n  \"whoEntityId\": \"142601234\",\n  \"whoEntityUrl\": \"https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Example of creating a diagnosis using ICD-11 code obtained from WHO ICD API search"}}, {"name": "List Member Diagnoses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}}}, {"name": "Get Diagnosis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Update Diagnosis", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"I10\",\n  \"description\": \"Essential (primary) hypertension\",\n  \"clinicalNote\": \"BP now controlled with current medication regimen. Patient showing excellent compliance.\",\n  \"status\": \"resolved\",\n  \"dateIdentified\": \"2023-06-01T00:00:00Z\",\n  \"source\": \"EHR import\",\n  \"confirmedBy\": \"Dr<PERSON>, MD\"\n}"}, "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}, {"name": "Delete Diagnosis", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/diagnoses/{{diagnosisID}}", "host": ["{{baseUrl}}"], "path": ["api", "diagnoses", "{{diagnosisID}}"]}}}]}, {"name": "Diagnosis Workflow (WHO ICD Integration)", "description": "Complete workflow demonstrating how to search WHO ICD API and create diagnoses", "item": [{"name": "Step 1: Search WHO ICD for Condition", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"type 2 diabetes\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/whoicd/search/combined", "host": ["{{baseUrl}}"], "path": ["whoicd", "search", "combined"]}, "description": "Search for diabetes in both ICD-10 and ICD-11 to find appropriate codes"}}, {"name": "Step 2: Get Detailed Entity Information", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/whoicd/entity/{{whoEntityId}}", "host": ["{{baseUrl}}"], "path": ["whoicd", "entity", "{{whoEntityId}}"]}, "description": "Get detailed information about the selected ICD entity (use ID from search results)"}}, {"name": "Step 3: Create Diagnosis with WHO ICD Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"icdCode\": \"5A11\",\n  \"description\": \"Type 2 diabetes mellitus\",\n  \"clinicalNote\": \"Pat<PERSON> presents with elevated HbA1c (8.5%) and classic symptoms. Family history positive for diabetes. Confirmed diagnosis using WHO ICD-11 classification.\",\n  \"status\": \"active\",\n  \"dateIdentified\": \"2023-06-20T00:00:00Z\",\n  \"source\": \"WHO ICD search\",\n  \"confirmedBy\": \"<PERSON><PERSON>, <PERSON>\",\n  \"whoEntityId\": \"142601234\",\n  \"whoEntityUrl\": \"https://icd.who.int/browse11/l-m/en#/http://id.who.int/icd/entity/142601234\",\n  \"icdVersion\": \"ICD-11\",\n  \"releaseId\": \"2024-01\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "Create the diagnosis using the ICD code and information obtained from WHO ICD API"}}, {"name": "Step 4: <PERSON><PERSON>fy Created Diagnosis", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/diagnoses", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "diagnoses"]}, "description": "List all diagnoses for the member to verify the new diagnosis was created"}}]}, {"name": "Member Medications", "item": [{"name": "Create Medication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 10mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"10mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Member reports 90% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}}}, {"name": "List Member Medications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications"]}}}, {"name": "Get Active Medications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/active", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "active"]}}}, {"name": "Get Medications by Type", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/by-type?type=prescribed", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "by-type"], "query": [{"key": "type", "value": "prescribed"}]}}}, {"name": "Get Medication", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Update Medication", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"medicationName\": \"Lisinopril 20mg\",\n  \"rxNormCode\": \"197361\",\n  \"dosage\": \"20mg\",\n  \"route\": \"oral\",\n  \"frequency\": \"daily\",\n  \"startDate\": \"2023-05-20T00:00:00Z\",\n  \"prescribedBy\": \"<PERSON><PERSON> <PERSON>\",\n  \"status\": \"active\",\n  \"adherenceNotes\": \"Dosage increased. Member reports 95% adherence\",\n  \"source\": \"EHR import\",\n  \"medicationType\": \"prescribed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}, {"name": "Discontinue Medication", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/members/{{memberID}}/medications/{{medicationID}}/discontinue", "host": ["{{baseUrl}}"], "path": ["api", "members", "{{memberID}}", "medications", "{{medicationID}}", "discontinue"]}}}, {"name": "Delete Medication", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/medications/{{medicationID}}", "host": ["{{baseUrl}}"], "path": ["api", "medications", "{{medicationID}}"]}}}]}]}