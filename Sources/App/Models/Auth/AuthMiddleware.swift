//
//  File.swift
//  
//
//  Created by <PERSON> on 1/30/23.
//

import Foundation
import Vapor
import Fluent

struct OrgRequestMiddleware: Middleware {
    func respond(to request: Request, chainingTo next: Responder) -> EventLoopFuture<Response> {
        if let org:String?            = request.query["org"], org != nil {
            guard let orgID = org       else { return request.eventLoop.future(error: NetworkError.error(type: .organization))  }
            guard orgID != "undefined"  else { return request.eventLoop.future(error:NetworkError.error(type: .organization)) }
            guard !orgID.isEmpty        else { return request.eventLoop.future(error:NetworkError.error(type: .organization)) }
            return next.respond(to: request)
        } else {
            return next.respond(to: request)
        }
    }
}


struct AuthMiddleware: Middleware {
    func respond(to request: Request, chainingTo next: Responder) -> EventLoopFuture<Response> {
        //healthcheck.
        if request.route?.path.last?.description == nil {
            return next.respond(to: request)
        } else if let unAuthRoute = request.route?.path.last?.description, noAuthRequired(route: unAuthRoute) {
            return next.respond(to: request)
        }
        else if let apiRoute = request.route?.path.first?.description, apiRoute == "api" {
//            guard let token = request.headers["X-API-Key"].last else {
//                return request.eventLoop.future(error: Abort(.unauthorized))
//            }
//            let cleanToken = token.replacingOccurrences(of: "Bearer", with: "").replacingOccurrences(of: " ", with: "")
//            return Token.query(on: request.db).filter(\.$value == cleanToken).first().flatMap { token in
//                guard let tk = token else {
//                    return request.eventLoop.future(error:Abort(.unauthorized))
//                }
//                return tk.isValid ? next.respond(to: request) : request.eventLoop.future(error:Abort(.unauthorized))
//            }
            return next.respond(to: request)
        }
        else {
            guard let token = request.headers["Authorization"].last else {
                return request.eventLoop.future(error: Abort(.unauthorized))
            }
            let cleanToken = token.replacingOccurrences(of: "Bearer", with: "").replacingOccurrences(of: " ", with: "")
            return Token.query(on: request.db).filter(\.$value == cleanToken).first().flatMap { token in
                guard let tk = token else {
                    return request.eventLoop.future(error:Abort(.unauthorized))
                }
                return tk.isValid ? next.respond(to: request) : request.eventLoop.future(error:Abort(.unauthorized))
            }
        }
    }
    
    fileprivate func noAuthRequired(route:String) -> Bool {
        return route == "constants" || route == "orgConstants" || route == "signup" || route == "memberSignup" || route == "login" || route == "memberLogin" || route == "whitelabels" || route == "import" || route == "networks" || route == "sms" || route == "failedSMS" || route == "mfaLogin" || route == "mfaVerify" || route == "mfaCreateUser" || route == "" || route == "webhook" || route == "search" || route == "health"
    }
}
