//
//  WHOICDExtensions.swift
//  
//
//  Created by <PERSON> on 6/22/25.
//

import Foundation
import Vapor

// MARK: - Error Handling

enum WHOICDError: Error, AbortError {
    case authenticationFailed(String)
    case searchFailed(String)
    case entityNotFound(String)
    case invalidRequest(String)
    case rateLimitExceeded
    case serviceUnavailable
    
    var status: HTTPResponseStatus {
        switch self {
        case .authenticationFailed:
            return .unauthorized
        case .searchFailed, .invalidRequest:
            return .badRequest
        case .entityNotFound:
            return .notFound
        case .rateLimitExceeded:
            return .tooManyRequests
        case .serviceUnavailable:
            return .serviceUnavailable
        }
    }
    
    var reason: String {
        switch self {
        case .authenticationFailed(let message):
            return "WHO ICD authentication failed: \(message)"
        case .searchFailed(let message):
            return "WHO ICD search failed: \(message)"
        case .entityNotFound(let message):
            return "WHO ICD entity not found: \(message)"
        case .invalidRequest(let message):
            return "Invalid WHO ICD request: \(message)"
        case .rateLimitExceeded:
            return "WHO ICD API rate limit exceeded"
        case .serviceUnavailable:
            return "WHO ICD service is currently unavailable"
        }
    }
}

// MARK: - Response Extensions

extension WHOICDEntity {
    /// Returns a simplified version of the entity for basic display
    var simplified: SimplifiedWHOICDEntity {
        return SimplifiedWHOICDEntity(
            id: self.id,
            title: self.title,
            code: self.code,
            definition: self.definition,
            browserUrl: self.browserUrl,
            score: self.score
        )
    }
}

struct SimplifiedWHOICDEntity: Content {
    let id: String?
    let title: String?
    let code: String?
    let definition: String?
    let browserUrl: String?
    let score: Double?
}

// MARK: - Search Helpers

extension WHOICDController {
    
    /// Performs a comprehensive search across both ICD-10 and ICD-11
    func searchBothVersions(req: Request, query: String) -> EventLoopFuture<CombinedICDSearchResponse> {
        let icd10Future = getAccessToken(req: req).flatMap { token in
            return self.performSearch(req: req, token: token, query: query, releaseId: "2019-04")
        }
        
        let icd11Future = getAccessToken(req: req).flatMap { token in
            return self.performSearch(req: req, token: token, query: query, releaseId: "2024-01")
        }
        
        return icd10Future.and(icd11Future).map { results in
            let (icd10Result, icd11Result) = results
            let icd10Entities = icd10Result.destinationEntities ?? []
            let icd11Entities = icd11Result.destinationEntities ?? []

            return CombinedICDSearchResponse(
                query: query,
                icd10Results: icd10Entities.map { $0.simplified },
                icd11Results: icd11Entities.map { $0.simplified },
                totalICD10Results: icd10Entities.count,
                totalICD11Results: icd11Entities.count
            )
        }
    }
}

struct CombinedICDSearchResponse: Content {
    let query: String
    let icd10Results: [SimplifiedWHOICDEntity]
    let icd11Results: [SimplifiedWHOICDEntity]
    let totalICD10Results: Int
    let totalICD11Results: Int
    let searchTimestamp: Date
    
    init(query: String, icd10Results: [SimplifiedWHOICDEntity], icd11Results: [SimplifiedWHOICDEntity], totalICD10Results: Int, totalICD11Results: Int) {
        self.query = query
        self.icd10Results = icd10Results
        self.icd11Results = icd11Results
        self.totalICD10Results = totalICD10Results
        self.totalICD11Results = totalICD11Results
        self.searchTimestamp = Date()
    }
}

// MARK: - Validation Extensions

extension WHOICDDiagnosisSearchRequest: Validatable {
    static func validations(_ validations: inout Validations) {
        validations.add("query", as: String.self, is: !.empty && .count(2...))
        validations.add("releaseId", as: String?.self, is: .nil || !.empty, required: false)
        validations.add("language", as: String?.self, is: .nil || .count(2...5), required: false)
    }
}

// MARK: - Utility Functions

extension WHOICDConfiguration {
    /// Validates that all required configuration values are present
    static func validateConfiguration() throws {
        guard !clientId.isEmpty else {
            throw WHOICDError.invalidRequest("WHO ICD Client ID is not configured")
        }
        
        guard !clientSecret.isEmpty else {
            throw WHOICDError.invalidRequest("WHO ICD Client Secret is not configured")
        }
        
        guard !baseURL.isEmpty else {
            throw WHOICDError.invalidRequest("WHO ICD Base URL is not configured")
        }
        
        guard !apiBaseURL.isEmpty else {
            throw WHOICDError.invalidRequest("WHO ICD API Base URL is not configured")
        }
    }
}

// MARK: - Caching Support (for future implementation)

struct WHOICDCacheKey {
    let query: String
    let releaseId: String
    let language: String
    
    var key: String {
        return "whoicd:\(releaseId):\(language):\(query.lowercased())"
    }
}

// MARK: - Logging Extensions

extension WHOICDController {
    
    func logSearchRequest(req: Request, query: String, releaseId: String) {
        req.logger.info("WHO ICD Search Request", metadata: [
            "query": .string(query),
            "releaseId": .string(releaseId),
            "userAgent": .string(req.headers.first(name: .userAgent) ?? "unknown"),
            "timestamp": .string(ISO8601DateFormatter().string(from: Date()))
        ])
    }
    
    func logSearchResponse(req: Request, query: String, resultCount: Int, duration: TimeInterval) {
        req.logger.info("WHO ICD Search Response", metadata: [
            "query": .string(query),
            "resultCount": .string("\(resultCount)"),
            "duration": .string("\(duration)ms"),
            "timestamp": .string(ISO8601DateFormatter().string(from: Date()))
        ])
    }
    
    func logError(req: Request, error: Error, context: String) {
        req.logger.error("WHO ICD Error", metadata: [
            "context": .string(context),
            "error": .string(error.localizedDescription),
            "timestamp": .string(ISO8601DateFormatter().string(from: Date()))
        ])
    }
}
