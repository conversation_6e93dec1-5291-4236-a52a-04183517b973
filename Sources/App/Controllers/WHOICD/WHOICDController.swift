//
//  WHOICDController.swift
//  
//
//  Created by <PERSON> on 6/22/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response Models

struct WHOICDTokenRequest: Content {
    let client_id: String
    let client_secret: String
    let scope: String
    let grant_type: String
    
    init() {
        self.client_id = WHOICDConfiguration.clientId
        self.client_secret = WHOICDConfiguration.clientSecret
        self.scope = WHOICDConfiguration.scope
        self.grant_type = "client_credentials"
    }
    
    func asURLEncoded() -> String {
        return "client_id=\(client_id)&client_secret=\(client_secret)&scope=\(scope)&grant_type=\(grant_type)"
    }
}

struct WHOICDTokenResponse: Content {
    let access_token: String
    let token_type: String
    let expires_in: Int
    let scope: String?
}

struct WHOICDSearchQuery: Content {
    let q: String // Search query
    let subtreeFilterUsesFoundationDescendants: Bool?
    let includeKeywordResult: Bool?
    let useFlexisearch: Bool?
    let flatResults: Bool?
    let highlightingEnabled: Bool?
    let medicalCodingMode: Bool?
    
    init(query: String, 
         subtreeFilterUsesFoundationDescendants: Bool = false,
         includeKeywordResult: Bool = true,
         useFlexisearch: Bool = true,
         flatResults: Bool = true,
         highlightingEnabled: Bool = true,
         medicalCodingMode: Bool = true) {
        self.q = query
        self.subtreeFilterUsesFoundationDescendants = subtreeFilterUsesFoundationDescendants
        self.includeKeywordResult = includeKeywordResult
        self.useFlexisearch = useFlexisearch
        self.flatResults = flatResults
        self.highlightingEnabled = highlightingEnabled
        self.medicalCodingMode = medicalCodingMode
    }
    
    func asQueryParams() -> String {
        var params = ["q=\(q.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? q)"]
        
        if let subtreeFilter = subtreeFilterUsesFoundationDescendants {
            params.append("subtreeFilterUsesFoundationDescendants=\(subtreeFilter)")
        }
        if let includeKeyword = includeKeywordResult {
            params.append("includeKeywordResult=\(includeKeyword)")
        }
        if let flexisearch = useFlexisearch {
            params.append("useFlexisearch=\(flexisearch)")
        }
        if let flat = flatResults {
            params.append("flatResults=\(flat)")
        }
        if let highlighting = highlightingEnabled {
            params.append("highlightingEnabled=\(highlighting)")
        }
        if let medicalCoding = medicalCodingMode {
            params.append("medicalCodingMode=\(medicalCoding)")
        }
        
        return params.joined(separator: "&")
    }
}

struct WHOICDSearchResult: Content {
    let destinationEntities: [WHOICDEntity]?
    let error: Bool?
    let errorMessage: String?
    let resultChopped: Bool?
    let wordSuggestionsChopped: Bool?
    let guessType: Int?
    let uniqueSearchId: String?
}

// MARK: - Flexible Value Handling for WHO ICD API
public enum FlexibleStringOrNumber: Codable {
    case string(String)
    case number(Int)
    case double(Double)
    case bool(Bool)

    public init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let stringValue = try? container.decode(String.self) {
            self = .string(stringValue)
        } else if let intValue = try? container.decode(Int.self) {
            self = .number(intValue)
        } else if let doubleValue = try? container.decode(Double.self) {
            self = .double(doubleValue)
        } else if let boolValue = try? container.decode(Bool.self) {
            self = .bool(boolValue)
        } else {
            throw DecodingError.typeMismatch(FlexibleStringOrNumber.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected String, Int, Double, or Bool"))
        }
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .string(let value):
            try container.encode(value)
        case .number(let value):
            try container.encode(value)
        case .double(let value):
            try container.encode(value)
        case .bool(let value):
            try container.encode(value)
        }
    }

    public var stringValue: String {
        switch self {
        case .string(let value):
            return value
        case .number(let value):
            return String(value)
        case .double(let value):
            return String(value)
        case .bool(let value):
            return String(value)
        }
    }

    public var boolValue: Bool? {
        switch self {
        case .bool(let value):
            return value
        case .string(let value):
            return Bool(value)
        case .number(let value):
            return value != 0
        case .double(let value):
            return value != 0.0
        }
    }
}

struct WHOICDEntity: Content {
    let id: String?
    let title: String?
    let definition: String?
    let longDefinition: String?
    let fullySpecifiedName: String?
    let diagnosticCriteria: String?
    let codingNote: String?
    let blockId: String?
    let codeRange: String?
    let classKind: String?
    let child: [String]?
    let parent: [String]?
    let relatedEntitiesInMaternalChapter: [String]?
    let relatedEntitiesInPerinatalChapter: [String]?
    let postcoordinationScale: [WHOICDPostcoordinationScale]?
    let browserUrl: String?
    let code: String?
    let codingHint: String?
    let source: String?
    let score: Double?
    let highlight: String?
    let titleIsAFragment: FlexibleStringOrNumber?
    let titleIsTopScore: FlexibleStringOrNumber?
    let entityType: FlexibleStringOrNumber?
    let important: FlexibleStringOrNumber?
    let descendants: [WHOICDEntity]?

    // Computed properties for easy access
    var entityTypeString: String? {
        return entityType?.stringValue
    }

    var titleIsAFragmentBool: Bool? {
        return titleIsAFragment?.boolValue
    }

    var titleIsTopScoreBool: Bool? {
        return titleIsTopScore?.boolValue
    }

    var importantBool: Bool? {
        return important?.boolValue
    }
}

struct WHOICDPostcoordinationScale: Content {
    let id: String?
    let axisName: String?
    let requiredPostcoordination: Bool?
    let allowMultipleValues: String?
    let scaleEntity: [String]?
}

struct WHOICDDiagnosisSearchRequest: Content {
    let query: String
    let releaseId: String?
    let language: String?
    
    init(query: String, releaseId: String = "2024-01", language: String = "en") {
        self.query = query
        self.releaseId = releaseId
        self.language = language
    }
}

struct WHOICDDiagnosisResponse: Content {
    let results: [WHOICDEntity]
    let totalResults: Int?
    let query: String
    let releaseId: String?
    let language: String?
    
    init(results: [WHOICDEntity], totalResults: Int? = nil, query: String, releaseId: String? = nil, language: String? = nil) {
        self.results = results
        self.totalResults = totalResults ?? results.count
        self.query = query
        self.releaseId = releaseId
        self.language = language
    }
}

// MARK: - Controller

struct WHOICDController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let whoicd = routes.grouped("whoicd")
        whoicd.post("search", use: searchDiagnosis)
        whoicd.post("search", "combined", use: searchCombined)
        whoicd.get("entity", ":entityId", use: getEntity)
        whoicd.post("search", "icd10", use: searchICD10)
        whoicd.post("search", "icd11", use: searchICD11)
        whoicd.get("token", "test", use: testToken)
        whoicd.get("health", use: healthCheck)
    }
    
    // MARK: - Main search endpoint
    func searchDiagnosis(req: Request) throws -> EventLoopFuture<WHOICDDiagnosisResponse> {
        let searchRequest = try req.content.decode(WHOICDDiagnosisSearchRequest.self)
        
        return getAccessToken(req: req).flatMap { token in
            return self.performSearch(req: req, token: token, query: searchRequest.query, releaseId: searchRequest.releaseId ?? "2024-01")
                .map { searchResult in
                    return WHOICDDiagnosisResponse(
                        results: searchResult.destinationEntities ?? [],
                        totalResults: searchResult.destinationEntities?.count,
                        query: searchRequest.query,
                        releaseId: searchRequest.releaseId,
                        language: searchRequest.language
                    )
                }
        }
    }
    
    // MARK: - ICD-10 specific search
    func searchICD10(req: Request) throws -> EventLoopFuture<WHOICDDiagnosisResponse> {
        let searchRequest = try req.content.decode(WHOICDDiagnosisSearchRequest.self)

        return getAccessToken(req: req).flatMap { token in
            return self.performSearch(req: req, token: token, query: searchRequest.query, releaseId: "2016") // ICD-10 release
                .map { searchResult in
                    return WHOICDDiagnosisResponse(
                        results: searchResult.destinationEntities ?? [],
                        totalResults: searchResult.destinationEntities?.count,
                        query: searchRequest.query,
                        releaseId: "2016",
                        language: searchRequest.language
                    )
                }
        }
    }

    // MARK: - ICD-11 specific search
    func searchICD11(req: Request) throws -> EventLoopFuture<WHOICDDiagnosisResponse> {
        let searchRequest = try req.content.decode(WHOICDDiagnosisSearchRequest.self)

        return getAccessToken(req: req).flatMap { token in
            return self.performSearch(req: req, token: token, query: searchRequest.query, releaseId: "2024-01") // ICD-11 release
                .map { searchResult in
                    return WHOICDDiagnosisResponse(
                        results: searchResult.destinationEntities ?? [],
                        totalResults: searchResult.destinationEntities?.count,
                        query: searchRequest.query,
                        releaseId: "2024-01",
                        language: searchRequest.language
                    )
                }
        }
    }
    
    // MARK: - Get specific entity by ID
    func getEntity(req: Request) throws -> EventLoopFuture<WHOICDEntity> {
        guard let entityId = req.parameters.get("entityId") else {
            throw Abort(.badRequest, reason: "Entity ID is required")
        }
        
        return getAccessToken(req: req).flatMap { token in
            return self.fetchEntity(req: req, token: token, entityId: entityId)
        }
    }
    
    // MARK: - Combined search endpoint
    func searchCombined(req: Request) throws -> EventLoopFuture<CombinedICDSearchResponse> {
        let searchRequest = try req.content.decode(WHOICDDiagnosisSearchRequest.self)
        return searchBothVersions(req: req, query: searchRequest.query)
    }

    // MARK: - Health check endpoint
    func healthCheck(req: Request) throws -> EventLoopFuture<[String: String]> {
        return getAccessToken(req: req).map { _ in
            return [
                "status": "healthy",
                "service": "WHO ICD API",
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "version": "1.0.0"
            ]
        }.recover { error in
            return [
                "status": "unhealthy",
                "service": "WHO ICD API",
                "error": error.localizedDescription,
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "version": "1.0.0"
            ]
        }
    }

    // MARK: - Test token endpoint
    func testToken(req: Request) throws -> EventLoopFuture<WHOICDTokenResponse> {
        return getAccessToken(req: req)
    }

    // MARK: - Internal Helper Methods

    internal func getAccessToken(req: Request) -> EventLoopFuture<WHOICDTokenResponse> {
        let tokenRequest = WHOICDTokenRequest()
        let tokenURL = URI(string: "\(WHOICDConfiguration.tokenBaseURL)\(WHOICDConfiguration.tokenEndpoint)")

        guard let data = tokenRequest.asURLEncoded().data(using: .utf8) else {
            return req.eventLoop.future(error: Abort(.badRequest, reason: "Failed to encode token request"))
        }

        return req.client.post(tokenURL, headers: [
            "Content-Type": "application/x-www-form-urlencoded",
            "API-Version": "v2"
        ]) { clientRequest in
            clientRequest.body = ByteBuffer(data: data)
        }.flatMapThrowing { response in
            guard response.status == .ok else {
                throw Abort(.unauthorized, reason: "Failed to get WHO ICD access token: \(response.status)")
            }
            return try response.content.decode(WHOICDTokenResponse.self)
        }.flatMap { tokenResponse in
            return req.eventLoop.future(tokenResponse)
        }
    }

    internal func performSearch(req: Request, token: WHOICDTokenResponse, query: String, releaseId: String) -> EventLoopFuture<WHOICDSearchResult> {
        let searchQuery = WHOICDSearchQuery(query: query)
        let searchURL = URI(string: "\(WHOICDConfiguration.apiBaseURL)/release/11/\(releaseId)/mms/search?\(searchQuery.asQueryParams())")

        return req.client.get(searchURL, headers: [
            "Authorization": "Bearer \(token.access_token)",
            "Accept": "application/json",
            "API-Version": "v2",
            "Accept-Language": "en"
        ]).flatMapThrowing { response in
            guard response.status == .ok else {
                throw Abort(.badRequest, reason: "WHO ICD search failed: \(response.status)")
            }
            return try response.content.decode(WHOICDSearchResult.self)
        }.flatMap { searchResult in
            return req.eventLoop.future(searchResult)
        }
    }

    private func fetchEntity(req: Request, token: WHOICDTokenResponse, entityId: String) -> EventLoopFuture<WHOICDEntity> {
        let entityURL = URI(string: "\(WHOICDConfiguration.apiBaseURL)/release/11/2024-01/mms/\(entityId)")

        return req.client.get(entityURL, headers: [
            "Authorization": "Bearer \(token.access_token)",
            "Accept": "application/json",
            "API-Version": "v2",
            "Accept-Language": "en"
        ]).flatMapThrowing { response in
            guard response.status == .ok else {
                throw Abort(.notFound, reason: "WHO ICD entity not found: \(response.status)")
            }
            return try response.content.decode(WHOICDEntity.self)
        }.flatMap { entity in
            return req.eventLoop.future(entity)
        }
    }
}
