openapi: 3.0.3
info:
  title: WHO ICD API Integration
  description: |
    Integration with the World Health Organization's International Classification of Diseases (ICD) API.
    Provides access to both ICD-10 and ICD-11 classifications for searching diagnoses and disease information.
    
    ## Features
    - Search across ICD-10 and ICD-11 classifications
    - Get detailed entity information
    - Combined search across both versions
    - Health monitoring and status checks
    
    ## Authentication
    This API requires JWT authentication. Include your token in the Authorization header:
    `Authorization: Bearer YOUR_JWT_TOKEN`
    
    ## Rate Limits
    Please be mindful of rate limits when making requests to avoid service disruption.
  version: 1.0.0
  contact:
    name: Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://your-api-domain.com
    description: Production server
  - url: https://staging-api-domain.com
    description: Staging server
  - url: http://localhost:8080
    description: Local development server

security:
  - BearerAuth: []

paths:
  /whoicd/search:
    post:
      summary: Search diagnoses
      description: Search for diagnoses across the default ICD release (ICD-11 2024-01)
      operationId: searchDiagnosis
      tags:
        - Diagnosis Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiagnosisSearchRequest'
            examples:
              diabetes:
                summary: Search for diabetes
                value:
                  query: "diabetes mellitus"
                  language: "en"
              covid:
                summary: Search for COVID-19
                value:
                  query: "covid-19"
                  releaseId: "2024-01"
                  language: "en"
      responses:
        '200':
          description: Successful search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiagnosisSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'

  /whoicd/search/icd10:
    post:
      summary: Search ICD-10 diagnoses
      description: Search specifically within ICD-10 classification (release 2019-04)
      operationId: searchICD10
      tags:
        - Diagnosis Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiagnosisSearchRequest'
      responses:
        '200':
          description: Successful ICD-10 search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiagnosisSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /whoicd/search/icd11:
    post:
      summary: Search ICD-11 diagnoses
      description: Search specifically within ICD-11 classification (release 2024-01)
      operationId: searchICD11
      tags:
        - Diagnosis Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiagnosisSearchRequest'
      responses:
        '200':
          description: Successful ICD-11 search results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiagnosisSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /whoicd/search/combined:
    post:
      summary: Combined ICD-10 and ICD-11 search
      description: Search across both ICD-10 and ICD-11 classifications simultaneously
      operationId: searchCombined
      tags:
        - Diagnosis Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiagnosisSearchRequest'
      responses:
        '200':
          description: Combined search results from both ICD versions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CombinedSearchResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /whoicd/entity/{entityId}:
    get:
      summary: Get entity details
      description: Retrieve detailed information about a specific ICD entity
      operationId: getEntity
      tags:
        - Entity Details
      parameters:
        - name: entityId
          in: path
          required: true
          description: The unique identifier of the ICD entity
          schema:
            type: string
          example: "142601234"
      responses:
        '200':
          description: Entity details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ICDEntity'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /whoicd/health:
    get:
      summary: Health check
      description: Check the health status of the WHO ICD API integration
      operationId: healthCheck
      tags:
        - Monitoring
      security: []
      responses:
        '200':
          description: Service health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'

  /whoicd/token/test:
    get:
      summary: Test authentication token
      description: Test the WHO ICD API authentication (for debugging purposes)
      operationId: testToken
      tags:
        - Authentication
      responses:
        '200':
          description: Token information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    DiagnosisSearchRequest:
      type: object
      required:
        - query
      properties:
        query:
          type: string
          minLength: 2
          description: Search term for diagnosis
          example: "diabetes mellitus"
        releaseId:
          type: string
          description: ICD release ID (optional)
          example: "2024-01"
        language:
          type: string
          minLength: 2
          maxLength: 5
          description: Language code (optional)
          example: "en"
          default: "en"

    DiagnosisSearchResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/SimplifiedICDEntity'
        totalResults:
          type: integer
          description: Total number of results found
        query:
          type: string
          description: The search query that was executed
        releaseId:
          type: string
          description: The ICD release that was searched
        language:
          type: string
          description: The language used for the search

    CombinedSearchResponse:
      type: object
      properties:
        query:
          type: string
          description: The search query that was executed
        icd10Results:
          type: array
          items:
            $ref: '#/components/schemas/SimplifiedICDEntity'
        icd11Results:
          type: array
          items:
            $ref: '#/components/schemas/SimplifiedICDEntity'
        totalICD10Results:
          type: integer
          description: Number of ICD-10 results
        totalICD11Results:
          type: integer
          description: Number of ICD-11 results
        searchTimestamp:
          type: string
          format: date-time
          description: When the search was performed

    SimplifiedICDEntity:
      type: object
      properties:
        id:
          type: string
          description: Unique entity identifier
        title:
          type: string
          description: Entity title/name
        code:
          type: string
          description: ICD code
        definition:
          type: string
          description: Entity definition
        browserUrl:
          type: string
          format: uri
          description: URL to view entity in WHO browser
        score:
          type: number
          format: double
          description: Search relevance score

    ICDEntity:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        definition:
          type: string
        longDefinition:
          type: string
        fullySpecifiedName:
          type: string
        diagnosticCriteria:
          type: string
        codingNote:
          type: string
        code:
          type: string
        classKind:
          type: string
        child:
          type: array
          items:
            type: string
        parent:
          type: array
          items:
            type: string
        browserUrl:
          type: string
          format: uri
        score:
          type: number
          format: double

    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
          description: Service health status
        service:
          type: string
          description: Service name
        timestamp:
          type: string
          format: date-time
          description: Status check timestamp
        version:
          type: string
          description: API version
        error:
          type: string
          description: Error message (if unhealthy)

    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
          description: WHO ICD API access token
        token_type:
          type: string
          description: Token type (usually "Bearer")
        expires_in:
          type: integer
          description: Token expiration time in seconds
        scope:
          type: string
          description: Token scope

    ErrorResponse:
      type: object
      properties:
        error:
          type: boolean
          example: true
        reason:
          type: string
          description: Error message

  responses:
    BadRequest:
      description: Bad request - invalid input
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: true
            reason: "Invalid WHO ICD request: Query parameter is required"

    Unauthorized:
      description: Unauthorized - authentication failed
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: true
            reason: "WHO ICD authentication failed: Invalid client credentials"

    NotFound:
      description: Not found - entity does not exist
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: true
            reason: "WHO ICD entity not found: Entity ID does not exist"

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: true
            reason: "WHO ICD API rate limit exceeded"

    ServiceUnavailable:
      description: Service unavailable
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: true
            reason: "WHO ICD service is currently unavailable"

tags:
  - name: Diagnosis Search
    description: Search for diagnoses and disease classifications
  - name: Entity Details
    description: Retrieve detailed information about specific entities
  - name: Monitoring
    description: Health checks and service monitoring
  - name: Authentication
    description: Authentication and token management
