//
//  WHOICDControllerTests.swift
//  
//
//  Created by <PERSON> on 6/22/25.
//

import XCTest
@testable import App
import XCTVapor

final class WHOICDControllerTests: XCTestCase {
    
    var app: Application!
    
    override func setUpWithError() throws {
        app = Application(.testing)
        try configure(app)
    }
    
    override func tearDownWithError() throws {
        app.shutdown()
    }
    
    // MARK: - Health Check Tests
    
    func testHealthCheck() throws {
        try app.test(.GET, "/whoicd/health") { res in
            XCTAssertEqual(res.status, .ok)
            
            let response = try res.content.decode([String: String].self)
            XCTAssertEqual(response["service"], "WHO ICD API")
            XCTAssertTrue(response["status"] == "healthy" || response["status"] == "unhealthy")
            XCTAssertNotNil(response["timestamp"])
            XCTAssertEqual(response["version"], "1.0.0")
        }
    }
    
    // MARK: - Search Tests
    
    func testSearchDiagnosis() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "diabetes")
        
        try app.test(.POST, "/whoicd/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            // Note: This test may fail if WHO ICD API is not accessible
            // In a real environment, you might want to mock the external API calls
            if res.status == .ok {
                let response = try res.content.decode(WHOICDDiagnosisResponse.self)
                XCTAssertEqual(response.query, "diabetes")
                XCTAssertNotNil(response.results)
            } else {
                // API might be unavailable in test environment
                XCTAssertTrue([.unauthorized, .serviceUnavailable, .badRequest].contains(res.status))
            }
        }
    }
    
    func testSearchICD10() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "hypertension")
        
        try app.test(.POST, "/whoicd/search/icd10", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            if res.status == .ok {
                let response = try res.content.decode(WHOICDDiagnosisResponse.self)
                XCTAssertEqual(response.query, "hypertension")
                XCTAssertEqual(response.releaseId, "2019-04")
                XCTAssertNotNil(response.results)
            } else {
                XCTAssertTrue([.unauthorized, .serviceUnavailable, .badRequest].contains(res.status))
            }
        }
    }
    
    func testSearchICD11() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "covid")
        
        try app.test(.POST, "/whoicd/search/icd11", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            if res.status == .ok {
                let response = try res.content.decode(WHOICDDiagnosisResponse.self)
                XCTAssertEqual(response.query, "covid")
                XCTAssertEqual(response.releaseId, "2024-01")
                XCTAssertNotNil(response.results)
            } else {
                XCTAssertTrue([.unauthorized, .serviceUnavailable, .badRequest].contains(res.status))
            }
        }
    }
    
    func testCombinedSearch() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "pneumonia")
        
        try app.test(.POST, "/whoicd/search/combined", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            if res.status == .ok {
                let response = try res.content.decode(CombinedICDSearchResponse.self)
                XCTAssertEqual(response.query, "pneumonia")
                XCTAssertNotNil(response.icd10Results)
                XCTAssertNotNil(response.icd11Results)
                XCTAssertNotNil(response.searchTimestamp)
            } else {
                XCTAssertTrue([.unauthorized, .serviceUnavailable, .badRequest].contains(res.status))
            }
        }
    }
    
    // MARK: - Validation Tests
    
    func testSearchWithEmptyQuery() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "")
        
        try app.test(.POST, "/whoicd/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .badRequest)
        }
    }
    
    func testSearchWithShortQuery() throws {
        let searchRequest = WHOICDDiagnosisSearchRequest(query: "a")
        
        try app.test(.POST, "/whoicd/search", beforeRequest: { req in
            try req.content.encode(searchRequest)
        }) { res in
            XCTAssertEqual(res.status, .badRequest)
        }
    }
    
    // MARK: - Entity Tests
    
    func testGetEntityWithInvalidId() throws {
        try app.test(.GET, "/whoicd/entity/invalid-id") { res in
            // Should return not found or bad request
            XCTAssertTrue([.notFound, .badRequest, .unauthorized].contains(res.status))
        }
    }
    
    // MARK: - Configuration Tests
    
    func testConfigurationValidation() throws {
        XCTAssertNoThrow(try WHOICDConfiguration.validateConfiguration())
        XCTAssertFalse(WHOICDConfiguration.clientId.isEmpty)
        XCTAssertFalse(WHOICDConfiguration.clientSecret.isEmpty)
        XCTAssertFalse(WHOICDConfiguration.baseURL.isEmpty)
        XCTAssertFalse(WHOICDConfiguration.apiBaseURL.isEmpty)
    }
    
    // MARK: - Model Tests
    
    func testWHOICDTokenRequest() throws {
        let tokenRequest = WHOICDTokenRequest()
        
        XCTAssertEqual(tokenRequest.client_id, WHOICDConfiguration.clientId)
        XCTAssertEqual(tokenRequest.client_secret, WHOICDConfiguration.clientSecret)
        XCTAssertEqual(tokenRequest.scope, WHOICDConfiguration.scope)
        XCTAssertEqual(tokenRequest.grant_type, "client_credentials")
        
        let urlEncoded = tokenRequest.asURLEncoded()
        XCTAssertTrue(urlEncoded.contains("client_id="))
        XCTAssertTrue(urlEncoded.contains("client_secret="))
        XCTAssertTrue(urlEncoded.contains("scope="))
        XCTAssertTrue(urlEncoded.contains("grant_type=client_credentials"))
    }
    
    func testWHOICDSearchQuery() throws {
        let searchQuery = WHOICDSearchQuery(query: "diabetes mellitus")
        
        XCTAssertEqual(searchQuery.q, "diabetes mellitus")
        XCTAssertEqual(searchQuery.medicalCodingMode, true)
        XCTAssertEqual(searchQuery.flatResults, true)
        
        let queryParams = searchQuery.asQueryParams()
        XCTAssertTrue(queryParams.contains("q=diabetes"))
        XCTAssertTrue(queryParams.contains("medicalCodingMode=true"))
        XCTAssertTrue(queryParams.contains("flatResults=true"))
    }
    
    func testSimplifiedWHOICDEntity() throws {
        let entity = WHOICDEntity(
            id: "123",
            title: "Test Disease",
            definition: "Test definition",
            longDefinition: nil,
            fullySpecifiedName: nil,
            diagnosticCriteria: nil,
            codingNote: nil,
            blockId: nil,
            codeRange: nil,
            classKind: nil,
            child: nil,
            parent: nil,
            relatedEntitiesInMaternalChapter: nil,
            relatedEntitiesInPerinatalChapter: nil,
            postcoordinationScale: nil,
            browserUrl: "https://example.com",
            code: "A01",
            codingHint: nil,
            source: nil,
            score: 0.95,
            highlight: nil,
            titleIsAFragment: nil,
            titleIsTopScore: nil,
            entityType: nil,
            important: nil,
            descendants: nil
        )
        
        let simplified = entity.simplified
        XCTAssertEqual(simplified.id, "123")
        XCTAssertEqual(simplified.title, "Test Disease")
        XCTAssertEqual(simplified.code, "A01")
        XCTAssertEqual(simplified.definition, "Test definition")
        XCTAssertEqual(simplified.browserUrl, "https://example.com")
        XCTAssertEqual(simplified.score, 0.95)
    }
}
